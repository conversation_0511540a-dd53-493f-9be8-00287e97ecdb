# Product Manager Demo / Ứng dụng Quản lý <PERSON> phẩm

<!-- Badges -->
[![Flutter](https://img.shields.io/badge/Flutter-3.7.2+-02569B?style=for-the-badge&logo=flutter&logoColor=white)](https://flutter.dev)
[![Dart](https://img.shields.io/badge/Dart-3.0+-0175C2?style=for-the-badge&logo=dart&logoColor=white)](https://dart.dev)
[![License](https://img.shields.io/badge/License-MIT-green?style=for-the-badge)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-Android%20%7C%20iOS-lightgrey?style=for-the-badge)](https://flutter.dev)

> 🇺🇸 English | 🇻🇳 [Tiếng Việt](README.vi.md) | 🇯🇵 日本語 (WIP)

### 📖 Overview

A comprehensive Flutter mobile application for product management featuring offline-first architecture, real-time synchronization, and modern UI/UX design. Built with Clean Architecture principles and industry best practices.

### ✨ Key Features

- 📋 **Product Management**: Complete CRUD operations for products
- 🔍 **Advanced Search**: Real-time search with category filtering
- 🖼️ **Image Gallery**: Multi-image support with zoom and Hero animations
- 📱 **Offline Support**: SQLite local storage with automatic sync
- 🎨 **Modern UI**: Material 3 design with responsive layouts
- 🚀 **Performance**: Optimized image caching and state management
- 🧪 **Mock API**: Built-in REST API simulation for development

### 🛠️ Tech Stack

#### Core Framework
- **Flutter** `^3.7.2` - Cross-platform mobile framework
- **Dart** `^3.0` - Programming language

#### State Management & Architecture
- **flutter_bloc** `^9.1.1` - Predictable state management
- **get_it** `^8.1.0` - Dependency injection
- **injectable** `^2.3.2` - Code generation for DI

#### Data & Networking
- **dio** `^5.8.0+1` - HTTP client
- **sqflite** `^2.4.2` - Local SQLite database
- **http_mock_adapter** `^0.6.1` - API mocking

#### UI & Media
- **flutter_screenutil** `^5.9.0` - Responsive design
- **cached_network_image** `^3.4.1` - Image caching
- **flutter_cache_manager** `^3.4.1` - Advanced cache management
- **image_picker** `^1.0.4` - Camera/gallery integration

#### Utilities
- **shared_preferences** `^2.5.3` - Simple data persistence
- **path_provider** `^2.1.4` - File system paths
- **equatable** `^2.0.7` - Value equality
- **intl** `^0.20.2` - Internationalization

### 🏗️ Architecture

This project follows **Clean Architecture** principles with clear separation of concerns:

```
lib/
├── 🎯 domain/          # Business logic layer
│   ├── entities/       # Core business objects
│   ├── repositories/   # Abstract contracts
│   └── usecases/       # Business rules
├── 💾 data/            # Data access layer
│   ├── models/         # Data transfer objects
│   ├── repositories/   # Repository implementations
│   ├── datasources/    # Local & remote data sources
│   └── services/       # Mock API services
├── 🎨 presentation/    # UI layer
│   ├── pages/          # Screen widgets
│   ├── blocs/          # State management
│   └── widgets/        # Reusable components
└── ⚙️ core/            # Shared utilities
    ├── services/       # Core services
    ├── utils/          # Helper functions
    └── mock/           # API interceptors
```

### 🚀 Getting Started

#### Prerequisites

- **Flutter SDK** `3.7.2` or higher
- **Dart SDK** `3.0` or higher
- **Android Studio** / **VS Code** with Flutter extensions
- **Android SDK** (for Android development)
- **Xcode** (for iOS development, macOS only)

#### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/product_manager_demo.git
   cd product_manager_demo
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code** (for dependency injection)
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Run the application**
   ```bash
   # Debug mode
   flutter run
   ```

### 📱 Screenshots

| Product List | Product Detail | Add/Edit Product |
|--------------|----------------|------------------|
| ![Product List](screenshots/product_list.png) | ![Product Detail](screenshots/product_detail.png) | ![Add Product](screenshots/add_product.png) |

### 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## Tiếng Việt

### 📖 Tổng quan

Ứng dụng Flutter quản lý sản phẩm toàn diện với kiến trúc offline-first, real-time synchronization và thiết kế UI/UX hiện đại. Được phát triển theo nguyên tắc Clean Architecture và industry best practices.

### ✨ Tính năng chính

- 📋 **Product Management**: Đầy đủ CRUD operations cho sản phẩm
- 🔍 **Advanced Search**: Real-time search với category filtering
- 🖼️ **Image Gallery**: Multi-image support với zoom và Hero animations
- 📱 **Offline Support**: SQLite local storage với automatic sync
- 🎨 **Modern UI**: Material 3 design với responsive layouts
- 🚀 **Performance**: Optimized image caching và state management
- 🧪 **Mock API**: Built-in REST API simulation cho development

### 🛠️ Tech Stack

#### Core Framework
- **Flutter** `^3.7.2` - Cross-platform mobile framework
- **Dart** `^3.0` - Programming language

#### State Management & Architecture
- **flutter_bloc** `^9.1.1` - Predictable state management
- **get_it** `^8.1.0` - Dependency injection
- **injectable** `^2.3.2` - Code generation cho DI

#### Data & Networking
- **dio** `^5.8.0+1` - HTTP client
- **sqflite** `^2.4.2` - Local SQLite database
- **http_mock_adapter** `^0.6.1` - API mocking

#### UI & Media
- **flutter_screenutil** `^5.9.0` - Responsive design
- **cached_network_image** `^3.4.1` - Image caching
- **flutter_cache_manager** `^3.4.1` - Advanced cache management
- **image_picker** `^1.0.4` - Camera/gallery integration

#### Utilities
- **shared_preferences** `^2.5.3` - Simple data persistence
- **path_provider** `^2.1.4` - File system paths
- **equatable** `^2.0.7` - Value equality
- **intl** `^0.20.2` - Internationalization

### 🏗️ Architecture

This project follows **Clean Architecture** principles with clear separation of concerns:

```
lib/
├── 🎯 domain/          # Business logic layer
│   ├── entities/       # Core business objects
│   ├── repositories/   # Abstract contracts
│   └── usecases/       # Business rules
├── 💾 data/            # Data access layer
│   ├── models/         # Data transfer objects
│   ├── repositories/   # Repository implementations
│   ├── datasources/    # Local & remote data sources
│   └── services/       # Mock API services
├── 🎨 presentation/    # UI layer
│   ├── pages/          # Screen widgets
│   ├── blocs/          # State management
│   └── widgets/        # Reusable components
└── ⚙️ core/            # Shared utilities
    ├── services/       # Core services
    ├── utils/          # Helper functions
    └── mock/           # API interceptors
```

### 🚀 Getting Started

#### Prerequisites

- **Flutter SDK** `3.7.2` or higher
- **Dart SDK** `3.0` or higher
- **Android Studio** / **VS Code** with Flutter extensions
- **Android SDK** (for Android development)
- **Xcode** (for iOS development, macOS only)

#### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/product_manager_demo.git
   cd product_manager_demo
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code** (for dependency injection)
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Run the application**
   ```bash
   # Debug mode
   flutter run

   # Release mode
   flutter run --release

   # Specific platform
   flutter run -d android
   flutter run -d ios
   ```

#### Development Setup

1. **Check Flutter installation**
   ```bash
   flutter doctor
   ```

2. **Enable developer options** on your device or start an emulator

3. **Hot reload** is available during development for instant updates

### 📱 Screenshots

| Product List | Product Detail | Add/Edit Product |
|--------------|----------------|------------------|
| ![Product List](screenshots/product_list.png) | ![Product Detail](screenshots/product_detail.png) | ![Add Product](screenshots/add_product.png) |

### 🧪 Testing

```bash
# Run all tests
flutter test

# Run tests with coverage
flutter test --coverage

# Run specific test file
flutter test test/widget_test.dart
```

### 📚 Documentation

- [Image Cache Guide](docs/IMAGE_CACHE_GUIDE.md) - Advanced image caching implementation
- [API Documentation](docs/api.md) - Mock API endpoints and usage

### 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

📘 [Tiếng Việt](README.vi.md) – Bấm vào đây để xem hướng dẫn bằng tiếng Việt

<div align="center">
  <p>Made with ❤️ by Flutter Community</p>
  <p>⭐ Star this repo if you find it helpful!</p>
</div>