/// Utility functions for text processing
class TextUtils {
  /// Vietnamese diacritics mapping for normalization
  static const Map<String, String> _vietnameseDiacritics = {
    // Lowercase vowels
    'à': 'a', 'á': 'a', 'ạ': 'a', 'ả': 'a', 'ã': 'a',
    'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ậ': 'a', 'ẩ': 'a', 'ẫ': 'a',
    'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ặ': 'a', 'ẳ': 'a', 'ẵ': 'a',
    'è': 'e', 'é': 'e', 'ẹ': 'e', 'ẻ': 'e', 'ẽ': 'e',
    'ê': 'e', 'ề': 'e', 'ế': 'e', 'ệ': 'e', 'ể': 'e', 'ễ': 'e',
    'ì': 'i', 'í': 'i', 'ị': 'i', 'ỉ': 'i', 'ĩ': 'i',
    'ò': 'o', 'ó': 'o', 'ọ': 'o', 'ỏ': 'o', 'õ': 'o',
    'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ộ': 'o', 'ổ': 'o', 'ỗ': 'o',
    'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ợ': 'o', 'ở': 'o', 'ỡ': 'o',
    'ù': 'u', 'ú': 'u', 'ụ': 'u', 'ủ': 'u', 'ũ': 'u',
    'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ự': 'u', 'ử': 'u', 'ữ': 'u',
    'ỳ': 'y', 'ý': 'y', 'ỵ': 'y', 'ỷ': 'y', 'ỹ': 'y',
    'đ': 'd',

    // Uppercase vowels
    'À': 'A', 'Á': 'A', 'Ạ': 'A', 'Ả': 'A', 'Ã': 'A',
    'Â': 'A', 'Ầ': 'A', 'Ấ': 'A', 'Ậ': 'A', 'Ẩ': 'A', 'Ẫ': 'A',
    'Ă': 'A', 'Ằ': 'A', 'Ắ': 'A', 'Ặ': 'A', 'Ẳ': 'A', 'Ẵ': 'A',
    'È': 'E', 'É': 'E', 'Ẹ': 'E', 'Ẻ': 'E', 'Ẽ': 'E',
    'Ê': 'E', 'Ề': 'E', 'Ế': 'E', 'Ệ': 'E', 'Ể': 'E', 'Ễ': 'E',
    'Ì': 'I', 'Í': 'I', 'Ị': 'I', 'Ỉ': 'I', 'Ĩ': 'I',
    'Ò': 'O', 'Ó': 'O', 'Ọ': 'O', 'Ỏ': 'O', 'Õ': 'O',
    'Ô': 'O', 'Ồ': 'O', 'Ố': 'O', 'Ộ': 'O', 'Ổ': 'O', 'Ỗ': 'O',
    'Ơ': 'O', 'Ờ': 'O', 'Ớ': 'O', 'Ợ': 'O', 'Ở': 'O', 'Ỡ': 'O',
    'Ù': 'U', 'Ú': 'U', 'Ụ': 'U', 'Ủ': 'U', 'Ũ': 'U',
    'Ư': 'U', 'Ừ': 'U', 'Ứ': 'U', 'Ự': 'U', 'Ử': 'U', 'Ữ': 'U',
    'Ỳ': 'Y', 'Ý': 'Y', 'Ỵ': 'Y', 'Ỷ': 'Y', 'Ỹ': 'Y',
    'Đ': 'D',
  };

  /// Removes Vietnamese diacritics from text for search normalization
  ///
  /// Example:
  /// ```dart
  /// TextUtils.removeDiacritics('Điện thoại') // returns 'Dien thoai'
  /// TextUtils.removeDiacritics('Máy tính') // returns 'May tinh'
  /// ```
  static String removeDiacritics(String text) {
    String result = text;

    _vietnameseDiacritics.forEach((diacritic, replacement) {
      result = result.replaceAll(diacritic, replacement);
    });

    return result;
  }

  /// Normalizes text for search by removing diacritics and converting to lowercase
  ///
  /// Example:
  /// ```dart
  /// TextUtils.normalizeForSearch('Điện Thoại') // returns 'dien thoai'
  /// TextUtils.normalizeForSearch('MÁY TÍNH') // returns 'may tinh'
  /// ```
  static String normalizeForSearch(String text) {
    return removeDiacritics(text.toLowerCase().trim());
  }

  /// Checks if search query matches text with diacritic-insensitive comparison
  ///
  /// Example:
  /// ```dart
  /// TextUtils.matchesSearch('Điện thoại', 'dien') // returns true
  /// TextUtils.matchesSearch('Máy tính', 'may tinh') // returns true
  /// TextUtils.matchesSearch('Laptop', 'điện') // returns false
  /// ```
  static bool matchesSearch(String text, String query) {
    if (query.isEmpty) return true;

    final normalizedText = normalizeForSearch(text);
    final normalizedQuery = normalizeForSearch(query);

    return normalizedText.contains(normalizedQuery);
  }
}
