import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';

/// Logging interceptor for Dio HTTP client
/// 
/// Provides comprehensive logging for API requests, responses, and errors
/// during development. Logs include timestamps, request details, response
/// information, and error traces for debugging purposes.
@injectable
class LoggingInterceptor extends Interceptor {
  static const String _separator = '============================================================';
  static const String _requestPrefix = '🚀 REQUEST';
  static const String _responsePrefix = '✅ RESPONSE';
  static const String _errorPrefix = '❌ ERROR';

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final timestamp = _getTimestamp();
    final requestId = _generateRequestId();
    
    _logRequest(options, timestamp, requestId);
    
    // Store request start time for response time calculation
    options.extra['_request_start_time'] = DateTime.now().millisecondsSinceEpoch;
    options.extra['_request_id'] = requestId;
    
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    final timestamp = _getTimestamp();
    final requestId = response.requestOptions.extra['_request_id'] ?? 'unknown';
    final startTime = response.requestOptions.extra['_request_start_time'] as int?;
    final responseTime = startTime != null 
        ? DateTime.now().millisecondsSinceEpoch - startTime 
        : null;
    
    _logResponse(response, timestamp, requestId, responseTime);
    
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final timestamp = _getTimestamp();
    final requestId = err.requestOptions.extra['_request_id'] ?? 'unknown';
    final startTime = err.requestOptions.extra['_request_start_time'] as int?;
    final responseTime = startTime != null 
        ? DateTime.now().millisecondsSinceEpoch - startTime 
        : null;
    
    _logError(err, timestamp, requestId, responseTime);
    
    super.onError(err, handler);
  }

  /// Log request details
  void _logRequest(RequestOptions options, String timestamp, String requestId) {
    final buffer = StringBuffer();
    
    buffer.writeln(_separator);
    buffer.writeln('$_requestPrefix [$requestId] - $timestamp');
    buffer.writeln(_separator);
    buffer.writeln('Method: ${options.method.toUpperCase()}');
    buffer.writeln('URL: ${options.uri}');
    
    // Log headers if present
    if (options.headers.isNotEmpty) {
      buffer.writeln('Headers:');
      options.headers.forEach((key, value) {
        buffer.writeln('  $key: $value');
      });
    }
    
    // Log query parameters if present
    if (options.queryParameters.isNotEmpty) {
      buffer.writeln('Query Parameters:');
      options.queryParameters.forEach((key, value) {
        buffer.writeln('  $key: $value');
      });
    }
    
    // Log request data if present
    if (options.data != null) {
      buffer.writeln('Request Data:');
      buffer.writeln(_formatData(options.data));
    }
    
    buffer.writeln(_separator);
    
    debugPrint(buffer.toString());
  }

  /// Log response details
  void _logResponse(Response response, String timestamp, String requestId, int? responseTime) {
    final buffer = StringBuffer();
    
    buffer.writeln(_separator);
    buffer.write('$_responsePrefix [$requestId] - $timestamp');
    if (responseTime != null) {
      buffer.write(' (${responseTime}ms)');
    }
    buffer.writeln();
    buffer.writeln(_separator);
    buffer.writeln('Status Code: ${response.statusCode}');
    buffer.writeln('Status Message: ${response.statusMessage ?? 'N/A'}');
    buffer.writeln('URL: ${response.requestOptions.uri}');
    
    // Log response headers if present
    if (response.headers.map.isNotEmpty) {
      buffer.writeln('Response Headers:');
      response.headers.map.forEach((key, value) {
        buffer.writeln('  $key: ${value.join(', ')}');
      });
    }
    
    // Log response data
    if (response.data != null) {
      buffer.writeln('Response Data:');
      buffer.writeln(_formatData(response.data));
    }
    
    buffer.writeln(_separator);
    
    debugPrint(buffer.toString());
  }

  /// Log error details
  void _logError(DioException error, String timestamp, String requestId, int? responseTime) {
    final buffer = StringBuffer();
    
    buffer.writeln(_separator);
    buffer.write('$_errorPrefix [$requestId] - $timestamp');
    if (responseTime != null) {
      buffer.write(' (${responseTime}ms)');
    }
    buffer.writeln();
    buffer.writeln(_separator);
    buffer.writeln('Error Type: ${error.type}');
    buffer.writeln('Error Message: ${error.message}');
    buffer.writeln('URL: ${error.requestOptions.uri}');
    buffer.writeln('Method: ${error.requestOptions.method.toUpperCase()}');
    
    // Log response data if available (for HTTP errors)
    if (error.response != null) {
      buffer.writeln('Status Code: ${error.response!.statusCode}');
      buffer.writeln('Status Message: ${error.response!.statusMessage ?? 'N/A'}');
      
      if (error.response!.data != null) {
        buffer.writeln('Error Response Data:');
        buffer.writeln(_formatData(error.response!.data));
      }
    }
    
    // Log stack trace if available
    if (error.stackTrace != null) {
      buffer.writeln('Stack Trace:');
      buffer.writeln(error.stackTrace.toString());
    }
    
    buffer.writeln(_separator);
    
    debugPrint(buffer.toString());
  }

  /// Format data for logging (pretty print JSON if possible)
  String _formatData(dynamic data) {
    try {
      if (data is Map || data is List) {
        // Pretty print JSON
        const encoder = JsonEncoder.withIndent('  ');
        return encoder.convert(data);
      } else if (data is String) {
        // Try to parse and pretty print if it's JSON string
        try {
          final decoded = json.decode(data);
          const encoder = JsonEncoder.withIndent('  ');
          return encoder.convert(decoded);
        } catch (e) {
          // If not JSON, return as is
          return data;
        }
      } else {
        return data.toString();
      }
    } catch (e) {
      return data.toString();
    }
  }

  /// Generate timestamp string
  String _getTimestamp() {
    final now = DateTime.now();
    return '${now.hour.toString().padLeft(2, '0')}:'
           '${now.minute.toString().padLeft(2, '0')}:'
           '${now.second.toString().padLeft(2, '0')}.'
           '${now.millisecond.toString().padLeft(3, '0')}';
  }

  /// Generate unique request ID for tracking
  String _generateRequestId() {
    return DateTime.now().millisecondsSinceEpoch.toString().substring(8);
  }
}
