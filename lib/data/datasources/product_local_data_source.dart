import '../../domain/entities/product.dart';

/// Abstract interface for local product data operations
///
/// This interface defines the contract for local storage operations
/// following the Repository pattern and Clean Architecture principles.
abstract class ProductLocalDataSource {
  /// Retrieves all products from local storage
  ///
  /// Returns a list of all stored products.
  /// Returns an empty list if no products are found.
  Future<List<Product>> getProducts();

  /// Retrieves a specific product by ID from local storage
  ///
  /// [id] The unique identifier of the product to retrieve
  /// Throws an exception if the product is not found
  Future<Product> getProduct(int id);

  /// Stores a new product in local storage
  ///
  /// [product] The product entity to store
  /// The product ID will be auto-generated by the database
  Future<void> insertProduct(Product product);

  /// Updates an existing product in local storage
  ///
  /// [product] The product entity with updated information
  /// The product must have a valid ID
  Future<void> updateProduct(Product product);

  /// Removes a product from local storage
  ///
  /// [id] The unique identifier of the product to delete
  Future<void> deleteProduct(int id);

  /// Removes all products from local storage
  ///
  /// This is typically used for cache clearing or data reset operations
  Future<void> clearProducts();

  /// Stores multiple products in local storage
  ///
  /// [products] List of product entities to store
  /// This method is optimized for batch operations
  Future<void> insertProducts(List<Product> products);
}
