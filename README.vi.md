# Product Manager Demo / Ứng dụng Quản lý <PERSON>ản phẩm

<!-- Badges -->
[![Flutter](https://img.shields.io/badge/Flutter-3.7.2+-02569B?style=for-the-badge&logo=flutter&logoColor=white)](https://flutter.dev)
[![Dart](https://img.shields.io/badge/Dart-3.0+-0175C2?style=for-the-badge&logo=dart&logoColor=white)](https://dart.dev)
[![License](https://img.shields.io/badge/License-MIT-green?style=for-the-badge)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-Android%20%7C%20iOS-lightgrey?style=for-the-badge)](https://flutter.dev)

> 🇺🇸 [English](README.md) | 🇻🇳 Tiếng Việt

## 📖 Tổng quan

Ứng dụng Flutter quản lý sản phẩm với kiến trúc offline-first, real-time synchronization và UI/UX hiện đại. Đ<PERSON><PERSON><PERSON> phát triển theo nguyên tắc Clean Architecture và industry best practices.

## ✨ Tính năng

- 📋 **Product Management**: Đầy đủ CRUD operations
- 🔍 **Advanced Search**: Real-time search với category filtering
- 🖼️ **Image Gallery**: Multi-image support với zoom và Hero animations
- 📱 **Offline Support**: SQLite local storage với automatic sync
- 🎨 **Modern UI**: Material 3 design với responsive layouts
- 🚀 **Performance**: Optimized image caching và state management

## 🛠️ Tech Stack

| Loại | Công nghệ | Mục đích |
|------|-----------|----------|
| **Framework** | Flutter `^3.7.2`, Dart `^3.0` | Cross-platform mobile development |
| **State Management** | flutter_bloc `^9.1.1` | Predictable state management |
| **Dependency Injection** | get_it `^8.1.0`, injectable `^2.3.2` | DI container và code generation |
| **Data & Networking** | dio `^5.8.0+1`, sqflite `^2.4.2` | HTTP client và local database |
| **UI & Media** | flutter_screenutil `^5.9.0`, cached_network_image `^3.4.1` | Responsive design và image caching |
| **Development** | http_mock_adapter `^0.6.1` | API mocking cho development |

## 🏗️ Architecture

Được xây dựng theo nguyên tắc **Clean Architecture**:

```
lib/
├── domain/          # Business logic layer
├── data/            # Data access layer
├── presentation/    # UI layer
└── core/            # Shared utilities
```

- **Domain**: Entities, use cases, repository contracts
- **Data**: Models, repository implementations, data sources
- **Presentation**: UI screens, state management (Bloc), widgets
- **Core**: Services, utilities, mock interceptors

## 🚀 Getting Started

### Prerequisites
- Flutter SDK `3.7.2+`
- Dart SDK `3.0+`
- Android Studio / VS Code với Flutter extensions

### Installation & Run

```bash
# Clone repository
git clone https://github.com/yourusername/product_manager_demo.git
cd product_manager_demo

# Install dependencies
flutter pub get

# Generate code (cho dependency injection)
flutter packages pub run build_runner build

# Run application
flutter run
```

### Development
```bash
# Check Flutter installation
flutter doctor

# Run tests
flutter test
```

## 📱 Screenshots

| Product List | Product Detail | Add/Edit Product |
|--------------|----------------|------------------|
| ![Product List](screenshots/product_list.png) | ![Product Detail](screenshots/product_detail.png) | ![Add Product](screenshots/add_product.png) |

## 📚 Documentation

- [Image Cache Guide](docs/IMAGE_CACHE_GUIDE.md) - Advanced image caching implementation

## 📄 License

Dự án này được license theo MIT License - xem file [LICENSE](LICENSE) để biết chi tiết.

---

📘 [English](README.md) – Click here to view the English documentation
