/// Utility class for generating product templates
///
/// This class provides predefined product templates organized by category
/// to support random product generation functionality.
class ProductTemplateGenerator {
  /// Private constructor to prevent instantiation
  ProductTemplateGenerator._();

  /// Product templates organized by category ID
  static const Map<int, List<Map<String, String>>> _productTemplates = {
    1: [
      // Electronics
      {'name': 'Smartphone', 'desc': '<PERSON>i<PERSON>n thoại thông minh cao cấp'},
      {'name': 'Laptop', 'desc': '<PERSON><PERSON><PERSON> tính xách tay hiệu năng cao'},
      {'name': '<PERSON> nghe', 'desc': '<PERSON> nghe không dây chất lượng'},
      {'name': 'Tablet', 'desc': '<PERSON><PERSON><PERSON> tính bảng đa năng cho công việc'},
      {'name': '<PERSON><PERSON><PERSON> hồ thông minh', 'desc': '<PERSON><PERSON><PERSON> hồ thông minh đa chức năng'},
      {'name': '<PERSON><PERSON><PERSON>', 'desc': '<PERSON><PERSON><PERSON> ảnh kỹ thuật số chuyên nghiệ<PERSON>'},
      {
        'name': '<PERSON><PERSON> Bluetooth',
        'desc': '<PERSON><PERSON> không dây âm thanh sống động',
      },
      {'name': 'Ổ cứng SSD', 'desc': 'Ổ cứng thể rắn tốc độ cao'},
    ],
    2: [
      // Books
      {
        'name': 'Sách lập trình',
        'desc': 'Hướng dẫn lập trình từ cơ bản đến nâng cao',
      },
      {'name': 'Tiểu thuyết', 'desc': 'Tác phẩm văn học hay nhất'},
      {'name': 'Sách kinh doanh', 'desc': 'Chiến lược kinh doanh hiệu quả'},
      {
        'name': 'Sách thiếu nhi',
        'desc': 'Truyện tranh và sách giáo dục cho trẻ',
      },
      {'name': 'Sách tự học', 'desc': 'Kỹ năng phát triển bản thân'},
      {'name': 'Sách nấu ăn', 'desc': 'Công thức nấu ăn ngon dễ làm'},
      {'name': 'Sách lịch sử', 'desc': 'Khám phá những câu chuyện lịch sử'},
      {'name': 'Sách khoa học', 'desc': 'Kiến thức khoa học phổ thông'},
    ],
    3: [
      // Fashion
      {'name': 'Áo thun', 'desc': 'Áo thun cotton cao cấp'},
      {'name': 'Quần jeans', 'desc': 'Quần jeans thời trang'},
      {'name': 'Giày thể thao', 'desc': 'Giày thể thao phong cách'},
      {'name': 'Túi xách', 'desc': 'Túi xách thời trang nữ'},
      {'name': 'Áo khoác', 'desc': 'Áo khoác phong cách trẻ trung'},
      {'name': 'Váy dạ tiệc', 'desc': 'Váy thanh lịch cho tiệc tùng'},
      {
        'name': 'Phụ kiện thời trang',
        'desc': 'Trang sức và phụ kiện đẹp',
      },
      {'name': 'Giày cao gót', 'desc': 'Giày cao gót thanh lịch'},
    ],
    4: [
      // Home Appliances
      {'name': 'Nồi cơm điện', 'desc': 'Nồi cơm điện thông minh'},
      {'name': 'Máy xay sinh tố', 'desc': 'Máy xay đa năng cho gia đình'},
      {'name': 'Bộ đồ ăn', 'desc': 'Bộ đồ ăn sứ cao cấp'},
      {'name': 'Máy hút bụi', 'desc': 'Máy hút bụi không dây tiện lợi'},
      {'name': 'Lò vi sóng', 'desc': 'Lò vi sóng đa chức năng'},
      {
        'name': 'Máy giặt mini',
        'desc': 'Máy giặt nhỏ gọn tiết kiệm điện',
      },
      {'name': 'Bàn ủi hơi nước', 'desc': 'Bàn ủi công nghệ hơi nước'},
      {'name': 'Tủ lạnh mini', 'desc': 'Tủ lạnh mini cho văn phòng'},
    ],
  };

  /// Get all available category IDs
  static List<int> get availableCategoryIds => _productTemplates.keys.toList();

  /// Get product templates for a specific category
  ///
  /// [categoryId] The category ID to get templates for
  /// Returns a list of product templates or empty list if category not found
  static List<Map<String, String>> getTemplatesForCategory(int categoryId) {
    return _productTemplates[categoryId] ?? [];
  }

  /// Get all product templates
  static Map<int, List<Map<String, String>>> get allTemplates =>
      _productTemplates;

  /// Check if a category has templates
  ///
  /// [categoryId] The category ID to check
  /// Returns true if the category has templates, false otherwise
  static bool hasCategoryTemplates(int categoryId) {
    return _productTemplates.containsKey(categoryId) &&
        _productTemplates[categoryId]!.isNotEmpty;
  }

  /// Get total number of templates across all categories
  static int get totalTemplateCount {
    return _productTemplates.values
        .map((templates) => templates.length)
        .fold(0, (sum, count) => sum + count);
  }
}
